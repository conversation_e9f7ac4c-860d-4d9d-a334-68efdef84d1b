<template>
  <BreadCrumb pageTitle="系统配置"/>
  
  <div class="system-config-container">
    <!-- 配置导航标签 -->
    <div class="config-tabs mb-30">
      <ul class="nav nav-tabs" id="configTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button 
            class="nav-link" 
            :class="{ active: activeTab === 'users' }"
            @click="activeTab = 'users'"
            type="button"
          >
            <i class="flaticon-user me-2"></i>
            用户管理
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button 
            class="nav-link" 
            :class="{ active: activeTab === 'roles' }"
            @click="activeTab = 'roles'"
            type="button"
          >
            <i class="flaticon-shield me-2"></i>
            角色权限
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button 
            class="nav-link" 
            :class="{ active: activeTab === 'settings' }"
            @click="activeTab = 'settings'"
            type="button"
          >
            <i class="flaticon-settings me-2"></i>
            系统设置
          </button>
        </li>
      </ul>
    </div>

    <!-- 用户管理标签页 -->
    <div v-show="activeTab === 'users'" class="tab-content">
      <div class="card border-0 rounded-3 bg-white shadow-sm">
        <div class="card-header bg-transparent border-bottom-0 pb-0">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title fw-bold mb-0">
              <i class="flaticon-user text-primary me-2"></i>
              用户账号管理
            </h5>
            <button class="btn btn-primary btn-sm" @click="showCreateUserModal = true">
              <i class="flaticon-plus me-1"></i>
              新建用户
            </button>
          </div>
        </div>
        <div class="card-body">
          <!-- 用户列表 -->
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>用户名</th>
                  <th>姓名</th>
                  <th>角色</th>
                  <th>状态</th>
                  <th>创建时间</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="user in users" :key="user.id">
                  <td>{{ user.username }}</td>
                  <td>{{ user.realName }}</td>
                  <td>
                    <span class="badge" :class="getRoleBadgeClass(user.role)">
                      {{ getRoleText(user.role) }}
                    </span>
                  </td>
                  <td>
                    <span class="badge" :class="user.status === 'active' ? 'bg-success' : 'bg-danger'">
                      {{ user.status === 'active' ? '正常' : '禁用' }}
                    </span>
                  </td>
                  <td>{{ formatDate(user.createTime) }}</td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary" @click="editUser(user)">
                        <i class="flaticon-edit"></i>
                      </button>
                      <button class="btn btn-outline-danger" @click="deleteUser(user)">
                        <i class="flaticon-delete"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 角色权限标签页 -->
    <div v-show="activeTab === 'roles'" class="tab-content">
      <div class="row">
        <div class="col-md-6">
          <div class="card border-0 rounded-3 bg-white shadow-sm">
            <div class="card-header bg-transparent border-bottom-0 pb-0">
              <h5 class="card-title fw-bold mb-0">
                <i class="flaticon-shield text-primary me-2"></i>
                角色列表
              </h5>
            </div>
            <div class="card-body">
              <div class="role-list">
                <div 
                  v-for="role in roles" 
                  :key="role.id"
                  class="role-item p-3 mb-3 border rounded-2"
                  :class="{ 'border-primary bg-light': selectedRole?.id === role.id }"
                  @click="selectRole(role)"
                >
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="mb-1">{{ role.name }}</h6>
                      <small class="text-muted">{{ role.description }}</small>
                    </div>
                    <span class="badge bg-secondary">{{ role.userCount }}人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card border-0 rounded-3 bg-white shadow-sm">
            <div class="card-header bg-transparent border-bottom-0 pb-0">
              <h5 class="card-title fw-bold mb-0">
                <i class="flaticon-key text-primary me-2"></i>
                权限设置
              </h5>
            </div>
            <div class="card-body">
              <div v-if="selectedRole" class="permissions-config">
                <h6 class="mb-3">{{ selectedRole.name }} 权限配置</h6>
                <div class="permission-groups">
                  <div v-for="group in permissionGroups" :key="group.id" class="permission-group mb-4">
                    <div class="form-check mb-2">
                      <input 
                        class="form-check-input" 
                        type="checkbox" 
                        :id="`group-${group.id}`"
                        :checked="isGroupSelected(group)"
                        @change="toggleGroup(group)"
                      >
                      <label class="form-check-label fw-bold" :for="`group-${group.id}`">
                        {{ group.name }}
                      </label>
                    </div>
                    <div class="permission-items ms-4">
                      <div v-for="permission in group.permissions" :key="permission.id" class="form-check mb-1">
                        <input 
                          class="form-check-input" 
                          type="checkbox" 
                          :id="`perm-${permission.id}`"
                          :checked="hasPermission(permission.id)"
                          @change="togglePermission(permission.id)"
                        >
                        <label class="form-check-label" :for="`perm-${permission.id}`">
                          {{ permission.name }}
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-3">
                  <button class="btn btn-primary" @click="savePermissions">
                    <i class="flaticon-save me-1"></i>
                    保存权限
                  </button>
                </div>
              </div>
              <div v-else class="text-center text-muted py-5">
                <i class="flaticon-shield display-4 mb-3"></i>
                <p>请选择一个角色来配置权限</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统设置标签页 -->
    <div v-show="activeTab === 'settings'" class="tab-content">
      <div class="row">
        <div class="col-md-8">
          <div class="card border-0 rounded-3 bg-white shadow-sm">
            <div class="card-header bg-transparent border-bottom-0 pb-0">
              <h5 class="card-title fw-bold mb-0">
                <i class="flaticon-settings text-primary me-2"></i>
                系统参数配置
              </h5>
            </div>
            <div class="card-body">
              <form @submit.prevent="saveSystemSettings">
                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label class="form-label">系统名称</label>
                    <input 
                      type="text" 
                      class="form-control" 
                      v-model="systemSettings.systemName"
                      placeholder="请输入系统名称"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">系统版本</label>
                    <input 
                      type="text" 
                      class="form-control" 
                      v-model="systemSettings.version"
                      placeholder="请输入系统版本"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">会话超时时间（分钟）</label>
                    <input 
                      type="number" 
                      class="form-control" 
                      v-model="systemSettings.sessionTimeout"
                      min="5"
                      max="1440"
                    >
                  </div>
                  <div class="col-md-6 mb-3">
                    <label class="form-label">密码最小长度</label>
                    <input 
                      type="number" 
                      class="form-control" 
                      v-model="systemSettings.minPasswordLength"
                      min="6"
                      max="20"
                    >
                  </div>

                </div>
                <div class="mt-4">
                  <button type="submit" class="btn btn-primary me-2">
                    <i class="flaticon-save me-1"></i>
                    保存设置
                  </button>
                  <button type="button" class="btn btn-outline-secondary" @click="resetSettings">
                    <i class="flaticon-refresh me-1"></i>
                    重置
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-0 rounded-3 bg-white shadow-sm">
            <div class="card-header bg-transparent border-bottom-0 pb-0">
              <h5 class="card-title fw-bold mb-0">
                <i class="flaticon-info text-primary me-2"></i>
                系统信息
              </h5>
            </div>
            <div class="card-body">
              <div class="system-info">
                <div class="info-item mb-3">
                  <label class="text-muted small">当前版本</label>
                  <div class="fw-bold">v1.0.0</div>
                </div>
                <div class="info-item mb-3">
                  <label class="text-muted small">最后更新</label>
                  <div class="fw-bold">{{ formatDate(new Date()) }}</div>
                </div>
                <div class="info-item mb-3">
                  <label class="text-muted small">在线用户</label>
                  <div class="fw-bold text-success">{{ onlineUsers }}</div>
                </div>
                <div class="info-item mb-3">
                  <label class="text-muted small">系统状态</label>
                  <div class="fw-bold text-success">
                    <i class="flaticon-check me-1"></i>
                    正常运行
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 创建用户模态框 -->
  <div class="modal fade" :class="{ show: showCreateUserModal }" :style="{ display: showCreateUserModal ? 'block' : 'none' }" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">新建用户</h5>
          <button type="button" class="btn-close" @click="showCreateUserModal = false"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="createUser">
            <div class="mb-3">
              <label class="form-label">用户名</label>
              <input type="text" class="form-control" v-model="newUser.username" required>
            </div>
            <div class="mb-3">
              <label class="form-label">姓名</label>
              <input type="text" class="form-control" v-model="newUser.realName" required>
            </div>
            <div class="mb-3">
              <label class="form-label">密码</label>
              <input type="password" class="form-control" v-model="newUser.password" required>
            </div>
            <div class="mb-3">
              <label class="form-label">角色</label>
              <select class="form-select" v-model="newUser.role" required>
                <option value="">请选择角色</option>
                <option v-for="role in roles" :key="role.id" :value="role.code">
                  {{ role.name }}
                </option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="showCreateUserModal = false">取消</button>
          <button type="button" class="btn btn-primary" @click="createUser">创建</button>
        </div>
      </div>
    </div>
  </div>
  <div v-if="showCreateUserModal" class="modal-backdrop fade show"></div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import BreadCrumb from '@/components/layouts/BreadCrumb.vue'
import {
  getUserList,
  createUser as apiCreateUser,
  deleteUser as apiDeleteUser,
  getRoleOptions,
  getRoleList,
  getRolePermissions,
  updateRolePermissions,
  getMenuTree,
  type SystemUser,
  type SystemRole,
  type UserQueryParams,
  type RoleQueryParams,
  type MenuPermission
} from '@/utils/api/user'
import {
  getSystemSettings,
  updateSystemSettings,
  resetSystemSettings,
  getOnlineUserCount,
  type SystemSettings
} from '@/utils/api/system'

// 本地类型定义（用于UI显示）
interface User {
  id: number
  username: string
  realName: string
  role: string
  status: 'active' | 'inactive'
  createTime: Date
}

interface Role {
  id: number
  code: string
  name: string
  description: string
  userCount: number
}

interface Permission {
  id: number
  name: string
}

interface PermissionGroup {
  id: number
  name: string
  permissions: Permission[]
}

// 响应式数据
const activeTab = ref('users')
const showCreateUserModal = ref(false)
const isLoading = ref(false)
const error = ref('')

// 用户数据
const users = ref<User[]>([])
const userPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 用户查询参数
const userQuery = reactive({
  userName: '',
  phonenumber: '',
  status: ''
})

const newUser = reactive({
  userName: '',
  nickName: '',
  password: '',
  phonenumber: '',
  email: '',
  status: '0', // 0-正常 1-停用
  roleIds: [] as number[],
  remark: ''
})

// 可选角色列表
const roleOptions = ref<SystemRole[]>([])

// 角色管理数据
const roles = ref<Role[]>([])
const roleLoading = ref(false)
const rolePagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const selectedRole = ref<Role | null>(null)

// 权限管理数据
const permissionGroups = ref<PermissionGroup[]>([])
const menuTree = ref<MenuPermission[]>([])
const rolePermissions = ref(new Set<number>())
const permissionLoading = ref(false)

// 系统设置
const systemSettings = reactive({
  systemName: '咖啡店管理系统',
  version: 'v1.0.0',
  sessionTimeout: 30,
  minPasswordLength: 8
})

const onlineUsers = ref(8)

// 方法
const getRoleBadgeClass = (role: string) => {
  switch (role) {
    case 'admin': return 'bg-danger'
    case 'manager': return 'bg-warning'
    case 'staff': return 'bg-info'
    default: return 'bg-secondary'
  }
}

const getRoleText = (role: string) => {
  switch (role) {
    case 'admin': return '系统管理员'
    case 'manager': return '门店经理'
    case 'staff': return '普通员工'
    default: return '未知角色'
  }
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN')
}

// 获取用户列表
const fetchUserList = async () => {
  try {
    isLoading.value = true
    error.value = ''

    const params: UserQueryParams = {
      pageNum: userPagination.pageNum,
      pageSize: userPagination.pageSize,
      ...userQuery
    }

    const response = await getUserList(params)

    // 转换API数据为UI数据格式
    users.value = (response.rows || []).map(apiUser => ({
      id: apiUser.userId || 0,
      username: apiUser.userName,
      realName: apiUser.nickName,
      role: apiUser.roleIds?.[0]?.toString() || 'staff',
      status: apiUser.status === '0' ? 'active' : 'inactive',
      createTime: apiUser.createTime ? new Date(apiUser.createTime) : new Date()
    }))

    userPagination.total = response.total || 0

    console.log(`✅ [系统配置] 用户列表加载成功: ${users.value.length}个用户`)

  } catch (err) {
    console.error('❌ [系统配置] 获取用户列表失败:', err)
    error.value = '获取用户列表失败'
    users.value = []
  } finally {
    isLoading.value = false
  }
}

// 获取角色选项
const fetchRoleOptions = async () => {
  try {
    const roles = await getRoleOptions()
    roleOptions.value = roles || []
    console.log(`✅ [系统配置] 角色选项加载成功: ${roleOptions.value.length}个角色`)
  } catch (err) {
    console.error('❌ [系统配置] 获取角色选项失败:', err)
    roleOptions.value = []
  }
}

// 获取角色列表
const fetchRoleList = async () => {
  try {
    roleLoading.value = true

    const params: RoleQueryParams = {
      pageNum: rolePagination.pageNum,
      pageSize: rolePagination.pageSize
    }

    const response = await getRoleList(params)

    // 转换API数据为UI数据格式
    roles.value = (response.rows || []).map(apiRole => ({
      id: apiRole.roleId || 0,
      code: apiRole.roleKey,
      name: apiRole.roleName,
      description: apiRole.remark || '暂无描述',
      userCount: 0 // 这里需要额外的API来获取用户数量，暂时设为0
    }))

    rolePagination.total = response.total || 0

    console.log(`✅ [系统配置] 角色列表加载成功: ${roles.value.length}个角色`)

  } catch (err) {
    console.error('❌ [系统配置] 获取角色列表失败:', err)
    roles.value = []
    rolePagination.total = 0
  } finally {
    roleLoading.value = false
  }
}

// 获取菜单权限树
const fetchMenuTree = async () => {
  try {
    permissionLoading.value = true
    const menus = await getMenuTree()
    menuTree.value = menus || []

    // 转换菜单树为权限组格式（用于UI显示）
    permissionGroups.value = convertMenuTreeToPermissionGroups(menus)

    console.log(`✅ [系统配置] 菜单权限树加载成功: ${menuTree.value.length}个菜单`)
  } catch (err) {
    console.error('❌ [系统配置] 获取菜单权限树失败:', err)
    menuTree.value = []
    permissionGroups.value = []
  } finally {
    permissionLoading.value = false
  }
}

// 转换菜单树为权限组格式
const convertMenuTreeToPermissionGroups = (menus: MenuPermission[]): PermissionGroup[] => {
  return menus.map(menu => ({
    id: menu.menuId || 0,
    name: menu.menuName,
    permissions: (menu.children || []).map(child => ({
      id: child.menuId || 0,
      name: child.menuName
    }))
  }))
}

// 获取系统设置
const fetchSystemSettings = async () => {
  try {
    const settings = await getSystemSettings()

    // 更新本地系统设置数据
    Object.assign(systemSettings, {
      systemName: settings.systemName || '咖啡店管理系统',
      version: settings.version || 'v1.0.0',
      sessionTimeout: settings.sessionTimeout || 30,
      minPasswordLength: settings.minPasswordLength || 8
    })

    console.log('✅ [系统配置] 系统设置加载成功')
  } catch (err) {
    console.error('❌ [系统配置] 获取系统设置失败:', err)
    // 使用默认设置
  }
}

// 获取在线用户数量
const fetchOnlineUserCount = async () => {
  try {
    const count = await getOnlineUserCount()
    onlineUsers.value = count
    console.log(`✅ [系统配置] 在线用户数量: ${count}`)
  } catch (err) {
    console.error('❌ [系统配置] 获取在线用户数量失败:', err)
    // 使用默认值
    onlineUsers.value = 8
  }
}

const editUser = (user: User) => {
  console.log('编辑用户:', user)
  // TODO: 实现编辑用户逻辑
}

const deleteUser = async (user: User) => {
  if (confirm(`确定要删除用户 ${user.realName} 吗？`)) {
    try {
      await apiDeleteUser(user.id)
      console.log(`✅ [系统配置] 用户删除成功: ${user.realName}`)
      await fetchUserList() // 重新加载列表
    } catch (err) {
      console.error('❌ [系统配置] 删除用户失败:', err)
      alert('删除用户失败，请稍后重试')
    }
  }
}

const createUser = async () => {
  if (!newUser.userName || !newUser.nickName || !newUser.password) {
    alert('请填写完整信息')
    return
  }

  try {
    const userData: SystemUser = {
      userName: newUser.userName,
      nickName: newUser.nickName,
      password: newUser.password,
      phonenumber: newUser.phonenumber,
      email: newUser.email,
      status: newUser.status,
      roleIds: newUser.roleIds,
      remark: newUser.remark
    }

    await apiCreateUser(userData)
    console.log(`✅ [系统配置] 用户创建成功: ${newUser.nickName}`)

    // 重置表单
    Object.assign(newUser, {
      userName: '',
      nickName: '',
      password: '',
      phonenumber: '',
      email: '',
      status: '0',
      roleIds: [],
      remark: ''
    })

    showCreateUserModal.value = false
    await fetchUserList() // 重新加载列表

  } catch (err) {
    console.error('❌ [系统配置] 创建用户失败:', err)
    alert('创建用户失败，请稍后重试')
  }
}

const selectRole = async (role: Role) => {
  selectedRole.value = role
  console.log('选中角色:', role)

  // 加载该角色的权限
  await loadRolePermissions(role.id)
}

// 加载角色权限
const loadRolePermissions = async (roleId: number) => {
  try {
    const permissions = await getRolePermissions(roleId)
    rolePermissions.value = new Set(permissions)
    console.log(`✅ [系统配置] 角色权限加载成功: ${permissions.length}个权限`)
  } catch (err) {
    console.error('❌ [系统配置] 获取角色权限失败:', err)
    rolePermissions.value = new Set()
  }
}

// 保存角色权限
const saveRolePermissions = async () => {
  if (!selectedRole.value) {
    alert('请先选择角色')
    return
  }

  try {
    const menuIds = Array.from(rolePermissions.value)
    await updateRolePermissions(selectedRole.value.id, menuIds)
    console.log(`✅ [系统配置] 角色权限保存成功: ${selectedRole.value.name}`)
    alert('权限保存成功')
  } catch (err) {
    console.error('❌ [系统配置] 保存角色权限失败:', err)
    alert('权限保存失败，请稍后重试')
  }
}



const isGroupSelected = (group: PermissionGroup) => {
  return group.permissions.every((perm: Permission) => rolePermissions.value.has(perm.id))
}

const hasPermission = (permissionId: number) => {
  return rolePermissions.value.has(permissionId)
}

const toggleGroup = (group: PermissionGroup) => {
  const allSelected = isGroupSelected(group)
  group.permissions.forEach((perm: Permission) => {
    if (allSelected) {
      rolePermissions.value.delete(perm.id)
    } else {
      rolePermissions.value.add(perm.id)
    }
  })
}

const togglePermission = (permissionId: number) => {
  if (rolePermissions.value.has(permissionId)) {
    rolePermissions.value.delete(permissionId)
  } else {
    rolePermissions.value.add(permissionId)
  }
}

const savePermissions = async () => {
  await saveRolePermissions()
}

const saveSystemSettings = async () => {
  try {
    const settingsData: Partial<SystemSettings> = {
      systemName: systemSettings.systemName,
      version: systemSettings.version,
      sessionTimeout: systemSettings.sessionTimeout,
      minPasswordLength: systemSettings.minPasswordLength
    }

    await updateSystemSettings(settingsData)
    console.log('✅ [系统配置] 系统设置保存成功')
    alert('系统设置保存成功！')
  } catch (err) {
    console.error('❌ [系统配置] 保存系统设置失败:', err)
    alert('保存系统设置失败，请稍后重试')
  }
}

const resetSettings = async () => {
  if (confirm('确定要重置所有设置吗？')) {
    try {
      await resetSystemSettings()

      // 重新加载系统设置
      await fetchSystemSettings()

      console.log('✅ [系统配置] 系统设置重置成功')
      alert('系统设置重置成功！')
    } catch (err) {
      console.error('❌ [系统配置] 重置系统设置失败:', err)
      alert('重置系统设置失败，请稍后重试')
    }
  }
}

onMounted(async () => {
  console.log('📋 [系统配置] 页面初始化开始')

  try {
    // 并行加载所有必要数据
    await Promise.all([
      fetchUserList(),
      fetchRoleOptions(),
      fetchRoleList(),
      fetchMenuTree(),
      fetchSystemSettings(),
      fetchOnlineUserCount()
    ])

    console.log('✅ [系统配置] 页面初始化完成')
  } catch (err) {
    console.error('❌ [系统配置] 页面初始化失败:', err)
  }
})
</script>

<style scoped>
.system-config-container {
  min-height: 600px;
}

.config-tabs .nav-tabs {
  border-bottom: 2px solid #e9ecef;
}

.config-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
  padding: 12px 20px;
  margin-right: 10px;
  border-radius: 8px 8px 0 0;
}

.config-tabs .nav-link:hover {
  color: var(--bs-primary);
  background-color: #f8f9fa;
}

.config-tabs .nav-link.active {
  color: var(--bs-primary);
  background-color: white;
  border-bottom: 2px solid var(--bs-primary);
}

.role-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.role-item:hover {
  background-color: #f8f9fa !important;
}

.permission-groups .permission-group {
  border-left: 3px solid #e9ecef;
  padding-left: 15px;
}

.permission-items {
  max-height: 200px;
  overflow-y: auto;
}

.system-info .info-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.system-info .info-item:last-child {
  border-bottom: none;
}

.modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.table th {
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.btn-group-sm .btn {
  padding: 4px 8px;
  font-size: 0.8rem;
}
</style>
